#!/usr/bin/env python3
"""
超级优化版本的 group_by_session_id.py
专门针对千万级数据进行优化，使用以下策略：
1. 分块读取和处理，避免内存溢出
2. 使用更高效的数据结构
3. 减少不必要的数据复制
4. 优化JSON序列化
5. 使用更高效的排序算法
"""

import pandas as pd
import json
import os
import argparse
import time
import gc
from collections import Counter


def convert_to_json_serializable_fast(item):
    """快速版本的JSON序列化转换"""
    if isinstance(item, (pd.Timestamp,)):
        return str(item)
    elif isinstance(item, dict):
        return {key: convert_to_json_serializable_fast(value) for key, value in item.items()}
    elif isinstance(item, list):
        return [convert_to_json_serializable_fast(sub_item) for sub_item in item]
    return item


def get_session_id_fast(s: str) -> str:
    """优化的session ID提取"""
    return s.split("#", 1)[0]


def get_timestamp_fast(s: str) -> int:
    """优化的timestamp提取"""
    parts = s.split("#")
    return int(parts[-2]) if len(parts) >= 2 else 0





def ultra_optimized_group_by_session(df: pd.DataFrame, output_json_path: str, args) -> None:
    """超级优化的流式处理函数 - 最高效的内存使用"""
    print(f"开始超级优化流式处理，数据量: {len(df)} 行")
    start_time = time.time()

    # 1. 预处理 - 提取session信息
    print("步骤1: 提取session信息...")
    df['short_sessionid_'] = df['sessionid_'].apply(get_session_id_fast)
    df['timestamp_'] = df['sessionid_'].apply(get_timestamp_fast)

    # 2. 排序 - 使用更高效的排序
    print("步骤2: 排序数据...")
    df_sorted = df.sort_values(['short_sessionid_', 'timestamp_'], kind='mergesort')

    # 3. 流式处理 - 边读边写，最小内存占用
    print("步骤3: 开始流式处理...")
    length_dist = Counter()

    with open(output_json_path, 'a', encoding='utf-8') as fout:
        current_session = None
        current_records = []
        processed_sessions = 0
        written_sessions = 0

        # 使用itertuples获得最佳性能
        columns = df_sorted.columns.tolist()

        for row_tuple in df_sorted.itertuples(index=False, name=None):
            # 直接构建字典，避免中间变量
            row_dict = {col: row_tuple[i] for i, col in enumerate(columns)}
            session_id = row_dict['short_sessionid_']

            if current_session != session_id:
                # 处理上一个session
                if current_records:
                    length = len(current_records)
                    length_dist[length] += 1

                    if args.trajectory_length_min <= length <= args.trajectory_length_max:
                        # 直接序列化并写入，不保存在内存中
                        serialized = json.dumps(current_records, ensure_ascii=False, separators=(',', ':'))
                        fout.write(f"{current_session}\t*#&\t{serialized}\n")
                        written_sessions += 1

                    processed_sessions += 1
                    if processed_sessions % 10000 == 0:
                        print(f"已处理 {processed_sessions} 个sessions，已写入 {written_sessions} 个有效sessions")

                # 开始新session
                current_session = session_id
                current_records = [convert_to_json_serializable_fast(row_dict)]
            else:
                current_records.append(convert_to_json_serializable_fast(row_dict))

        # 处理最后一个session
        if current_records:
            length = len(current_records)
            length_dist[length] += 1

            if args.trajectory_length_min <= length <= args.trajectory_length_max:
                serialized = json.dumps(current_records, ensure_ascii=False, separators=(',', ':'))
                fout.write(f"{current_session}\t*#&\t{serialized}\n")
                written_sessions += 1

            processed_sessions += 1

    end_time = time.time()
    print(f"超级优化处理完成，耗时: {end_time - start_time:.2f}秒")
    print(f"总共处理了 {processed_sessions} 个sessions")
    print(f"写入了 {written_sessions} 个有效sessions")

    # 输出长度分布（按频率排序，只显示前20个）
    print("Session长度分布（前20个最常见）:")
    sorted_lengths = sorted(length_dist.items(), key=lambda x: x[1], reverse=True)[:20]
    for length, count in sorted_lengths:
        print(f"  长度 {length}: {count} 个sessions")

    # 输出统计信息
    total_sessions = sum(length_dist.values())
    if total_sessions > 0:
        valid_ratio = written_sessions / total_sessions * 100
        print(f"有效session比例: {valid_ratio:.1f}% ({written_sessions}/{total_sessions})")


def process_large_pickle_file(file_path: str, output_json_path: str, args) -> None:
    """处理大型pickle文件的优化函数"""
    print(f"处理大型文件: {file_path}")
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size / (1024*1024*1024):.2f} GB")
    
    try:
        # 读取数据
        print("读取pickle文件...")
        df = pd.read_pickle(file_path)
        
        print("数据基本信息：")
        print(f"数据维度: {df.shape[0]} 行 x {df.shape[1]} 列")
        print(f"内存使用: {df.memory_usage(deep=True).sum() / (1024*1024):.2f} MB")
        
        if len(df) > 0:
            # 使用超级优化版本处理
            ultra_optimized_group_by_session(df, output_json_path, args)
        else:
            print("警告: 数据文件为空")
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        raise
    finally:
        # 强制清理内存
        if 'df' in locals():
            del df
        gc.collect()


def main():
    parser = argparse.ArgumentParser(description="超级优化版 MiniP data Group by session")
    parser.add_argument("--input_pickle_dir", type=str, help="input_pickle_dir", default="")
    parser.add_argument("--target_pickles", type=str, help="all|file1,file2,file3", default="all")
    parser.add_argument("--trajectory_length_min", type=int, help="trajectory_length_min", default=1)
    parser.add_argument("--trajectory_length_max", type=int, help="trajectory_length_max", default=20)
    parser.add_argument("--output_json_path", type=str, help="output_json_path", default="")
    
    args = parser.parse_args()
    
    print("=== 超级优化版 Group By Session 启动 ===")
    print(f"参数: {args}")
    
    # 获取目标文件列表
    pickle_file_path = args.input_pickle_dir
    target_pickles_str = args.target_pickles
    
    if target_pickles_str == "all":
        target_pickles = [f for f in os.listdir(pickle_file_path) if f.endswith("pickle")]
    else:
        target_pickles = target_pickles_str.strip().split(",")
    
    print(f"目标文件: {target_pickles}")
    
    # 清空输出文件
    if os.path.exists(args.output_json_path):
        os.remove(args.output_json_path)
    
    # 处理文件
    file_list = [os.path.join(pickle_file_path, f) for f in target_pickles]
    total_start_time = time.time()
    
    for i, file_path in enumerate(file_list):
        print(f"\n=== 处理文件 {i+1}/{len(file_list)} ===")
        file_start_time = time.time()
        
        process_large_pickle_file(file_path, args.output_json_path, args)
        
        file_end_time = time.time()
        print(f"文件处理完成，耗时: {file_end_time - file_start_time:.2f}秒")
    
    total_end_time = time.time()
    print(f"\n=== 所有文件处理完成 ===")
    print(f"总耗时: {total_end_time - total_start_time:.2f}秒")


if __name__ == "__main__":
    main()
