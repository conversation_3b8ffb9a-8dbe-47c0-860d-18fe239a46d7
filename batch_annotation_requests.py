#!/usr/bin/env python3
"""
批量生成注释任务的脚本
基于提供的curl命令转换为requests实现
"""

import requests
import json
import time
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_requests.log'),
        logging.StreamHandler()
    ]
)

class AnnotationBatchProcessor:
    def __init__(self):
        self.base_url = "http://mmfinderdrannotationsvr.polaris:8080/annotations/tasks"
        self.headers = {
            'Content-Type': 'application/json'
        }
        # 固定的app信息
        self.app_id = "wx6c03ed6dfa30c735"
        self.app_name = "golflive"
        
    def create_request_payload(self, query: str) -> Dict[str, Any]:
        """创建请求负载"""
        return {
            "app_id": self.app_id,
            "app_name": self.app_name,
            "query": query
        }
    
    def send_single_request(self, query: str) -> Dict[str, Any]:
        """发送单个请求"""
        payload = self.create_request_payload(query)
        
        try:
            logging.info(f"发送请求: {query}")
            response = requests.post(
                self.base_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            result = {
                'query': query,
                'status_code': response.status_code,
                'response': response.json() if response.content else {},
                'success': True
            }
            
            logging.info(f"请求成功: {query} - 状态码: {response.status_code}")
            return result
            
        except requests.exceptions.RequestException as e:
            error_result = {
                'query': query,
                'status_code': getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                'error': str(e),
                'success': False
            }
            logging.error(f"请求失败: {query} - 错误: {str(e)}")
            return error_result
    
    def batch_process(self, queries: List[str], delay: float = 1.0) -> List[Dict[str, Any]]:
        """批量处理查询列表"""
        results = []
        total_queries = len(queries)
        
        logging.info(f"开始批量处理 {total_queries} 个查询")
        
        for i, query in enumerate(queries, 1):
            logging.info(f"处理进度: {i}/{total_queries}")
            
            result = self.send_single_request(query.strip())
            results.append(result)
            
            # 添加延迟避免请求过于频繁
            if i < total_queries and delay > 0:
                time.sleep(delay)
        
        # 统计结果
        successful = sum(1 for r in results if r['success'])
        failed = total_queries - successful
        
        logging.info(f"批量处理完成: 成功 {successful}, 失败 {failed}")
        
        return results
    
    def save_results(self, results: List[Dict[str, Any]], filename: str = "batch_results.json"):
        """保存结果到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logging.info(f"结果已保存到: {filename}")
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")


def main():
    """主函数示例"""
    # 示例查询列表
    sample_queries = """给我创建个广州麓湖乡村俱乐部的比赛，第一洞-1
第二洞抓鸟了
这洞打了个 4
4 杆洞打了 5
刚说错了，应该是-2
第7洞打了个+1杆
➕1
-1
我这洞是 +1
这洞我 -1 
这一洞我平标准杆
吞了个柏忌
坏了，打了Double Par
一杆进洞
哎，打了个双柏忌
保帕了
刚打了个小鸟球
打了个老鹰球
打了个信天翁
刚才两轮先射鹰，再抓鸟
刚忘说了，打了个小鸟球，这一轮保帕
第 4 洞我好像说错了，你检查下，应该是 0
这洞我 -1，ella 保帕，bob柏忌
这洞我打鸟，小白打了par，ella 柏忌
刚才那轮记错了，我是标准杆
这洞我保帕，小白打了双柏忌，bob 柏忌
我上一洞打了多少分
我今天哪个洞打的最差
我打了几个鹰
我在第几洞打了鹰
bob 打了几个小鸟
ella 前两洞是几分
我们三个人倒数第二洞打了多少
我们三个人在最后一洞分别打了多少
我现在杆数多少
我现在领先小白多少杆
我前 9 得分多少
我后 9 得分多少
 bob 前 9 和 后 9 得分分别多少
我们三个人总差分别多少
我们三个人总杆分别多少
我们三个人8421得分是多少
我们三个人pk得分是多少""".split("\n")
    
    valid_queries = [query.strip() for query in sample_queries if query.strip()]
    
    # 创建处理器
    processor = AnnotationBatchProcessor()
    
    # 批量处理
    results = processor.batch_process(valid_queries, delay=0.3)
    
    # 保存结果
    processor.save_results(results)
    
    # 打印摘要
    successful = sum(1 for r in results if r['success'])
    print(f"\n处理完成:")
    print(f"总数: {len(results)}")
    print(f"成功: {successful}")
    print(f"失败: {len(results) - successful}")


if __name__ == "__main__":
    main()
